import React, { useCallback, useMemo } from 'react';
import { IColDef } from '@/types/AgGridTypes';
import CustomAgGrid from '../agGrid/AgGrid';
import { IHistoryGridProps } from './historyGridTypes';
import { useLanguage } from '@/hooks/useLanguage';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { HistoryServiceHook } from '@/api/history/useHistory';
import { GetHistoryDto, HistoryPropertyType } from '@/api/history/history.types';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { getTranslatedProperties } from '@/constant/HistoryKeys';
import { ICellRendererParams } from 'ag-grid-community';

/**
 * A reusable history grid component that displays change history for a given entity
 *
 * @param props - The component props
 * @returns A React component
 */
const HistoryGrid: React.FC<IHistoryGridProps> = (props) => {
  const { entityKey, entityId, customColumnDefs, className, gridId, loading } = props;

  const { t } = useLanguage();

  const {
    data: historyData,
    isLoading,
    isFetching,
  } = HistoryServiceHook.useEntity<GetHistoryDto[]>(`${entityKey}/${entityId}`, {
    enabled: Boolean(entityId),
  });

  const propertiesCellRenderer = (params: { value: string }) => {
    const translatedProperties = getTranslatedProperties();
    const translatedProperty = translatedProperties[params.value];
    return translatedProperty || params.value;
  };

  const valueCellRenderer = useCallback(
    (params: ICellRendererParams<GetHistoryDto>) => {
      const fieldName = params.colDef?.field as keyof GetHistoryDto;

      switch (params?.data?.propertyDataType) {
        case HistoryPropertyType.BOOLEAN:
          return params.data[fieldName] === 'true' ? t('common.yes') : t('common.no');
        case HistoryPropertyType.DATE:
          return dateFormatter(params.data[fieldName]) || '';
        case HistoryPropertyType.CURRENCY:
          return `$${params.data[fieldName] || 0}`;
        case HistoryPropertyType.STRING:
          return params.data[fieldName];
        default:
          break;
      }
    },
    [t]
  );

  // Default column definitions for the history grid
  const defaultColumnDefs = useMemo<IColDef[]>(
    () => [
      {
        field: 'property',
        headerName: t('historyGrid.property'),
        sortable: true,
        unSortIcon: true,
        minWidth: 220,
        flex: 1,
        visible: true,
        cellRenderer: propertiesCellRenderer,
      },
      {
        field: 'oldValue',
        headerName: t('historyGrid.oldValue'),
        sortable: true,
        unSortIcon: true,
        minWidth: 200,
        flex: 1,
        visible: true,
        cellRenderer: valueCellRenderer,
      },
      {
        field: 'newValue',
        headerName: t('historyGrid.newValue'),
        sortable: true,
        unSortIcon: true,
        minWidth: 200,
        flex: 1,
        visible: true,
        cellRenderer: valueCellRenderer,
      },
      {
        field: 'createdAt',
        headerName: t('historyGrid.dateTime'),
        sortable: true,
        unSortIcon: true,
        minWidth: 200,
        flex: 1,
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return dateFormatter(params.value);
        },
      },
      {
        field: 'updatedByName',
        headerName: t('historyGrid.modifiedBy'),
        sortable: true,
        unSortIcon: true,
        minWidth: 180,
        flex: 1,
        visible: true,
      },
    ],
    [t, valueCellRenderer]
  );

  // Use custom column definitions if provided, otherwise use default
  const columnDefs = customColumnDefs || defaultColumnDefs;

  // Default column properties
  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      sortable: true,
    }),
    []
  );

  return (
    <div className={`${className || ''}`}>
      <CustomAgGrid
        rowData={historyData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        gridId={gridId || GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
        loading={loading || isLoading || isFetching}
        className="!h-[82vh]"
      />
    </div>
  );
};

export default HistoryGrid;

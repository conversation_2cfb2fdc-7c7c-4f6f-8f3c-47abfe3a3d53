import { IColDef } from '@/types/AgGridTypes';
import { RefObject } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { historyKeys } from '@/constant/HistoryKeys';

/**
 * History record interface representing a change in an entity
 */
export interface IHistoryRecord {
  id: string;
  property: string;
  oldValue: string | number | null;
  newValue: string | number | null;
  dateTime: string;
  modifiedBy: string;
}

/**
 * Props for the HistoryGrid component
 */
export interface IHistoryGridProps {
  /**
   * The entity key to fetch history for
   */
  entityKey: (typeof historyKeys)[keyof typeof historyKeys];

  /**
   * Optional title for the history grid
   */
  title?: string;

  /**
   * Optional custom column definitions
   */
  customColumnDefs?: IColDef[];

  /**
   * The entity ID to fetch history for
   */
  entityId: string | undefined;

  /**
   * Optional grid reference
   */
  gridRef?: RefObject<AgGridReact>;

  /**
   * Optional class name for styling
   */
  className?: string;

  /**
   * Optional grid ID
   */
  gridId?: string;

  /**
   * Optional flag to show loading state
   */
  loading?: boolean;
}

/**
 * Mock data generator function type
 */
export type MockDataGeneratorFn = (entityKey: string) => IHistoryRecord[];

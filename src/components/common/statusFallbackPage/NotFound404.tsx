import Icon from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';
import { NotFound404SVGComponent } from '@/assets/icons/notfound404SVGComponent';

interface INotFount404Props {
  title?: string;
  description?: string;
  extraNode?: React.ReactElement;
}

const NotFound404 = ({ title, description, extraNode }: INotFount404Props) => {
  const { t } = useLanguage();

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Icon component={NotFound404SVGComponent} />
      <div className="text-gray-400 font-semibold text-2xl mt-6">
        {title || t('statusFallbackPage.notFound404.title')}
      </div>
      <div className="text-gray-400 font-medium text-base mt-2">
        {description || t('statusFallbackPage.notFound404.description')}
      </div>
      <div className="mt-2">{extraNode && extraNode}</div>
    </div>
  );
};

export default NotFound404;

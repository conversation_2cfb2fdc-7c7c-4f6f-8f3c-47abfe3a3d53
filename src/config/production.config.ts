import { ConfigSchema } from '@customTypes/ConfigSchema';

const productionConfig: ConfigSchema = {
  appName: 'Lumigo',
  apiUrl: 'https://example.com',
  baseUrl: 'https://api.hapito.app',
  showDetailedErrors: false,
  notification: {
    duration: 5,
    placement: 'topRight',
    pauseOnHover: false,
    className: 'notify-wrapper',
  },
  AxiosDefaultConfig: {
    timeout: 1000,
    retries: 3,
    retryDelay: 2000,
    maxRefreshAttempt: 3,
  },
  ContextMenuConfig: {
    minimumSubMenuSize: 190,
  },
  dateFormate: 'DD/MM/YYYY hh:mm A',
  dateFormateWithoutTime: 'DD/MM/YYYY',
  timeFormate12: 'hh:mm A',
  timeFormate24: 'HH:mm',
  is12HoursFormate: false,
  maxPriceSetScheduleLimit: 15,
  minPasswordLength: 8,
  maxPasswordLength: 16,
  otpLength: 4,
  units: {
    distance: 'km',
    weight: 'lb',
    dimension: 'In',
  },
  mapCenter: {
    lat: 45.508888,
    lng: -73.561668,
  },
};

export default productionConfig;

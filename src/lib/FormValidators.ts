import { formErrorRegex } from '@/constant/Regex';
import { translator } from '@/i18n/languageLoader';
import { FormInstance } from 'antd';
import { Rule, RuleObject } from 'antd/es/form';
import { File } from 'buffer';

export const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>, length?: number) => {
  if (
    event.key === 'e' ||
    event.key === 'E' ||
    event.key === '+' ||
    event.key === '-' ||
    event.key === '.' ||
    (length && event.currentTarget.value.length > length)
  ) {
    event.preventDefault();
  }
};
export const validateCountryAndValue = (
  form: FormInstance,
  countryField: string,
  valueField: string,
  required?: boolean
) => {
  return async (_: unknown, value: string) => {
    const country = form.getFieldValue(countryField);
    if (!country && !value && required) {
      throw new Error(`Please select your country & add your ${valueField}`);
    }
    if (!country && value) {
      throw new Error('Please select your country');
    }
    if (!value && country) {
      throw new Error(`Please enter your ${valueField}`);
    }
    if (formErrorRegex.NoMultipleWhiteSpaces && !formErrorRegex.NoMultipleWhiteSpaces.test(value)) {
      throw new Error('No multiple white spaces allowed');
    }
    return Promise.resolve();
  };
};
export const validateMaskedInput = (
  value: string,
  expectedLength: number,
  errorMessage: string
): Promise<void> => {
  if (value !== undefined) {
    if (value.includes('_') || value.length < expectedLength) {
      return Promise.reject(new Error(errorMessage));
    }
  }

  return Promise.resolve();
};

export const numberFieldValidator = (
  event: React.KeyboardEvent<HTMLInputElement>,
  { allowDecimals = false, allowNegative = false }
) => {
  const key = event.key;
  const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];
  if (allowDecimals) {
    if ((event.target as HTMLInputElement).value.includes('.') && key === '.') {
      event.preventDefault();
      return;
    }
    allowedKeys.push('.');
  }
  if (allowNegative) {
    allowedKeys.push('-');
  }

  if (!formErrorRegex.onlyDigits.test(key) && !allowedKeys.includes(key)) {
    event.preventDefault();
  }
};

export const noWhitespaceValidator = (_: RuleObject, value: string, message: string) => {
  if (!formErrorRegex.NO_SPACES.test(value)) {
    return Promise.reject(message);
  }
  return Promise.resolve();
};

export const minimumValueValidator = (_: Rule, value: string) => {
  if (Number(value) < 1) {
    return Promise.reject(translator('common.errors.valueMustBeMoreThan1'));
  }
  return Promise.resolve();
};

export const formSizeValidator = (value: { file: File }, size: number, message?: string) => {
  const file = Array.isArray(value) ? value[0] : value?.file || value;
  if (file && file.size > size * 1024 * 1024) {
    return Promise.reject(
      new Error(message || translator('ordersPage.attachmentsTab.fileSizeValidation'))
    );
  }
  return Promise.resolve();
};

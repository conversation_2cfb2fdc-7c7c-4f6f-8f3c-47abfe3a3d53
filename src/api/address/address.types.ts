export interface CreateAddressDto {
  name: string;
  companyName: string;
  email: string;
  countryCode: string;
  phoneNumber: string;
  phoneExtension: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  province: string;
  postalCode: string;
  latitude: string;
  longitude: string;
  zone: string;
  country: string;
  notes: string;
  isFavoriteForPickup: boolean;
  isFavoriteForDelivery: boolean;
  isDefaultForPickup: boolean;
  isDefaultForDelivery: boolean;
  customerId?: string;
}

export interface GetAddressDto extends CreateAddressDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  updatedByName? : string;
}

export interface GetAllAddressDto {
  total: number;
  pageNumber: number;
  pageSize: number;
  data: GetAddressDto[];
}

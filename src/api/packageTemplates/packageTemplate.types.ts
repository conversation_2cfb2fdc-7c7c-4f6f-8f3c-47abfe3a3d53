export interface CreateOrderItemDto {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  status: string;
  capabilities: string[];
  dimensionsRequired: boolean;
  weightRequired: boolean;
  maxWeight: number;
  maxVolume: number;
  length: number;
  width: number;
  height: number;
  weight: number;
  priceCalculationRules: {
    basePrice: number;
    weightFactor: number;
    distanceFactor: number;
  };
  requiresSignature: boolean;
  requiresInsurance: boolean;
  specialHandlingInstructions: string;
  vehicleTypeRestrictions: string[];
  availableZones: string[];
  metadata: {
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface ResponseOrderItemDto extends CreateOrderItemDto {
  id: string;
  orderId: string;
  createdAt: string;
  updatedAt: string;
  packageTemplateName?: string;
}

export interface IOrderItemPaginatedResponse {
  data: ResponseOrderItemDto[];
  total: number;
  skip: number;
  limit: number;
}

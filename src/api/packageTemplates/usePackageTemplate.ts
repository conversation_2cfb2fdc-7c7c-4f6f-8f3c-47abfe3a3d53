import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { PackageTemplateService } from './packageTemplate.service';
import { CreateOrderItemDto, ResponseOrderItemDto } from './packageTemplate.types';

export const packageTemplateService = new PackageTemplateService(apiClient.getAxiosInstance());

export const packageTemplateServiceHook = createEntityHooks<
  ResponseOrderItemDto,
  CreateOrderItemDto,
  CreateOrderItemDto
>(QueryHookKey.Orders, packageTemplateService);

import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { HistoryService } from './history.service';
import { GetHistoryDto } from './history.types';

export const historyService = new HistoryService(apiClient.getAxiosInstance());

export const HistoryServiceHook = createEntityHooks<GetHistoryDto, GetHistoryDto, GetHistoryDto>(
  QueryHookKey.filUploads,
  historyService
);

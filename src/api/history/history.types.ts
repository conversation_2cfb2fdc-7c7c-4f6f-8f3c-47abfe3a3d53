export interface GetHistoryDto {
  id: string;
  entity: string;
  entityId: string;
  tenantId: string;
  property: string;
  propertyDataType: HistoryPropertyType;
  oldValue: string;
  newValue: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export enum HistoryPropertyType {
  DATE = 'date',
  CURRENCY = 'currency',
  BOOLEAN = 'boolean',
  STRING = 'string',
}

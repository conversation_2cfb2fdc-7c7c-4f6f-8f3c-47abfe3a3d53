import { FilterParams, PaginationParams } from '@/api/core/types';
import { translator } from '@/i18n/languageLoader';
import { OrderStatusEnums } from '@/types/enums/orderStatus';

export const pageSizeDropdown = [
  { label: 20, value: 20 },
  { label: 50, value: 50 },
  { label: 100, value: 100 },
];

export const defaultPagination: PaginationParams & FilterParams = {
  pageNumber: 1,
  pageSize: 100,
};

export const orderStatusOptions = [
  {
    label: translator('ordersPage.statusValues.draft'),
    value: OrderStatusEnums.DRAFT,
  },
  {
    label: translator('ordersPage.submitted'),
    value: OrderStatusEnums.SUBMITTED,
  },
  {
    label: translator('ordersPage.statusValues.assigned'),
    value: OrderStatusEnums.ASSIGNED,
  },
  {
    label: translator('ordersPage.statusValues.pending'),
    value: OrderStatusEnums.PENDING,
  },
  {
    label: translator('ordersPage.statusValues.goingForPickup'),
    value: OrderStatusEnums.GOING_FOR_PICKUP,
  },
  {
    label: translator('ordersPage.statusValues.pickedUp'),
    value: OrderStatusEnums.PICKED_UP,
  },
  {
    label: translator('ordersPage.statusValues.goingForDelivery'),
    value: OrderStatusEnums.GOING_FOR_DELIVERY,
  },
  {
    label: translator('ordersPage.delivered'),
    value: OrderStatusEnums.COMPLETED,
  },
  {
    label: translator('ordersPage.statusValues.cancelled'),
    value: OrderStatusEnums.CANCELLED,
  },
];

export const MEME_TYPES = {
  pdf: 'application/pdf',
  csv: 'text/csv',
  image: 'image/*',
};

export const imageMimeTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
  'image/tiff',
  'image/x-icon',
  'image/svg+xml',
];

export const excelMimeType = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];

export const textMimeType = ['text/csv'];

export const getOrderStatusText = (status: string) => {
  switch (status) {
    case OrderStatusEnums.DRAFT:
      return translator('ordersPage.statusValues.draft');
    case OrderStatusEnums.SUBMITTED:
      return translator('ordersPage.submitted');
    case OrderStatusEnums.PENDING:
      return translator('ordersPage.statusValues.pending');
    case OrderStatusEnums.GOING_FOR_PICKUP:
      return translator('ordersPage.statusValues.goingForPickup');
    case OrderStatusEnums.PICKED_UP:
      return translator('ordersPage.statusValues.pickedUp');
    case OrderStatusEnums.GOING_FOR_DELIVERY:
      return translator('ordersPage.statusValues.goingForDelivery');
    case OrderStatusEnums.ASSIGNED:
      return translator('ordersPage.statusValues.assigned');
    case OrderStatusEnums.COMPLETED:
      return translator('ordersPage.delivered');
    case OrderStatusEnums.CANCELLED:
      return translator('ordersPage.statusValues.cancelled');
    default:
      return status;
  }
};

export const StringOperators = [
  {
    value: 'contains',
    label: 'Contain',
  },
  {
    value: 'notContains',
    label: 'Not contains',
  },
  {
    value: 'startsWith',
    label: 'Starts with',
  },
  {
    value: 'endsWith',
    label: 'Ends with',
  },
  {
    value: 'eq',
    label: 'Equals',
  },
  {
    value: 'neq',
    label: 'Not equals',
  },
  {
    value: 'in',
    label: 'In',
  },
  {
    value: 'notIn',
    label: 'Not in',
  },
];

export const NumberOperators = [
  {
    value: 'eq',
    label: 'Equals',
  },
  {
    value: 'neq',
    label: 'Not Equals',
  },
  {
    value: 'gt',
    label: 'Greater than',
  },
  {
    value: 'gte',
    label: 'Greater than or equal',
  },
  {
    value: 'lt',
    label: 'Less Than',
  },
  {
    value: 'lte',
    label: 'Less than or equal to',
  },
  {
    value: 'between',
    label: 'Between',
  },
  {
    value: 'in',
    label: 'In',
  },
  {
    value: 'notIn',
    label: 'Not in',
  },
];
export const DateOperators = [
  {
    value: 'eq',
    label: 'Equals',
  },
  {
    value: 'neq',
    label: 'Not Equals',
  },
  {
    value: 'gt',
    label: 'After',
  },
  {
    value: 'lt',
    label: 'Before',
  },
  {
    value: 'between',
    label: 'Between',
  },
];

export const filterOperator: Record<string, string> = {
  equals: 'eq',
  notEquals: 'neq',
  greaterThan: 'gt',
  greaterThanOrEqual: 'gte',
  lessThan: 'lt',
  lessThanOrEqual: 'lte',
  like: 'like',
  iLike: 'ilike',
  startsWith: 'startsWith',
  endsWith: 'endsWith',
  in: 'in',
  notIn: 'notIn',
  isNull: 'isNull',
  isNotNull: 'isNotNull',
  between: 'between',
  contains: 'contains',
  notContains: 'notContains',
};

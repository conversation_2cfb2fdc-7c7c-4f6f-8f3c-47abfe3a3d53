import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IColDef } from '@/types/AgGridTypes';
import { CellClickedEvent, ICellRendererParams, ValueFormatterParams } from 'ag-grid-community';
import { Button, Form, Input, InputNumber } from 'antd';
import {
  ICreatePriceBreakdownDTO,
  IModelOpenState,
  IPriceBreakdown,
  IResponsePriceBreakdownDto,
} from './orderPriceBreakdown.types';
import { useCallback, useState } from 'react';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { EditPopupWhiteIcon } from '@/assets/icons/editPopupIconWhite';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import { formErrorRegex } from '@/constant/Regex';
import Icon from '@ant-design/icons';
import { EyeIcon, HistoryIcon } from '@/assets';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { Rule, RuleObject } from 'antd/es/form';
import { orderServiceHook } from '@/api/orders/useOrders';
import { IResponseOrderDto } from '@/api/orders/order.types';
import { useParams } from 'react-router-dom';
import { numberFieldValidator } from '@/lib/FormValidators';

const OrdersPricesBreakdownComponent = () => {
  const [isModifyPricingModelOpen, setIsModifyPricingModelOpen] = useState<IModelOpenState>({
    open: false,
    isEdit: false,
    customPriceId: undefined,
  });
  const { id } = useParams();
  const { t } = useLanguage();
  ('');
  const {
    data: priceBreakdown,
    refetch: refetchPriceBreakdown,
    isLoading,
    isFetching,
  } = orderServiceHook.useEntity<IResponsePriceBreakdownDto>(
    `${id}/custom-pricing-summary` as string,
    {
      enabled: Boolean(id),
      retry: 0,
    }
  );

  const { data: orderDetails } = orderServiceHook.useEntity<IResponseOrderDto>(id as string, {
    enabled: Boolean(id),
    retry: 0,
  });

  const updateOrderMutation = orderServiceHook.useUpdate<ICreatePriceBreakdownDTO>();

  const addNewCustomPrice = async (values: ICreatePriceBreakdownDTO) => {
    try {
      await updateOrderMutation.mutateAsync({
        id: id as string,
        data: {
          ...values,
          customModifierName: values?.customModifierName,
          customModifierAmount: values?.customModifierAmount,
          description: values?.description || 'N/A',
        },
      });

      await refetchPriceBreakdown();
      setIsModifyPricingModelOpen({ open: false, isEdit: false, customPriceId: undefined });
    } catch (error) {
      // console.log(error);
    }
  };

  const [customPricingForm] = Form.useForm();

  const viewPriceHandler = useCallback(
    (params: ICellRendererParams | CellClickedEvent) => {
      customPricingForm.setFieldsValue(params.data);
      setIsModifyPricingModelOpen({
        open: true,
        isEdit: true,
        customPriceId: params.data?.id,
      });
    },
    [customPricingForm]
  );

  const colDefs: IColDef[] = [
    {
      field: 'customModifierName',
      headerName: t('ordersPage.priceBreakDown.colDefs.appliedModifier'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 300,
      flex: 1,
    },
    {
      field: 'customModifierAmount',
      headerName: t('ordersPage.priceBreakDown.colDefs.appliedCharges'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
      valueFormatter: (params: ValueFormatterParams<IPriceBreakdown>) => {
        return `$${params.value || 0}`;
      },
    },
    {
      field: `description`,
      headerName: t('ordersPage.priceBreakDown.colDefs.description'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 150,
      flex: 1,
      cellRenderer: (params: ICellRendererParams<IPriceBreakdown>) => {
        return params.value || 'N/A';
      },
    },
    {
      field: 'action',
      headerName: t('vehiclePage.colDefs.action'),
      pinned: 'right',
      maxWidth: 80,
      minWidth: 80,
      sortable: false,
      resizable: false,
      visible: true,
      cellRenderer: (params: ICellRendererParams<IPriceBreakdown>) => {
        return (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params?.data?.createdAt as string)}
                  </span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() => viewPriceHandler(params)}
            />
          </div>
        );
      },
    },
  ];

  const amountValidator = (_: Rule, value: number) => {
    if (value > 10000000) {
      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
    }
    if (value === 0) {
      return Promise.reject(new Error(t('ordersPage.priceBreakDown.negativeOrPositiveValue')));
    }
    return Promise.resolve();
  };

  const nameValidator = (_: RuleObject, value: string) => {
    const existingNames = priceBreakdown?.data.some(
      (item) =>
        item.customModifierName?.toLowerCase() === value?.toLowerCase()?.trimStart()?.trimEnd()
    );
    if (existingNames) {
      return Promise.reject(new Error(t('ordersPage.priceBreakDown.nameAlreadyExists')));
    }
    return Promise.resolve();
  };

  return (
    <div className="h-full flex flex-col gap-4 pr-4">
      <CustomModal
        modalTitle={
          isModifyPricingModelOpen.isEdit
            ? t('ordersPage.priceBreakDown.customPricingModelTitleEditMode')
            : t('ordersPage.priceBreakDown.customPricingModelTitle')
        }
        modalDescription={
          isModifyPricingModelOpen.isEdit
            ? t('ordersPage.priceBreakDown.customPricingModelDescEditMode')
            : t('ordersPage.priceBreakDown.customPricingModelDesc')
        }
        open={isModifyPricingModelOpen.open}
        destroyOnClose
        maskClosable={false}
        onCancel={() =>
          setIsModifyPricingModelOpen({ open: false, isEdit: false, customPriceId: undefined })
        }
        className="!w-[450px] !h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModifyPricingModelOpen({
                  open: false,
                  isEdit: false,
                  customPriceId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {!isModifyPricingModelOpen.isEdit && (
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="addCustomPrice"
                loading={updateOrderMutation.isPending}
              >
                {t('common.add')}
              </Button>
            )}
          </div>
        }
        width={450}
      >
        <div className="w-full md:min-w-[400px]">
          <Form
            name="addCustomPrice"
            className="custom-form"
            layout={'vertical'}
            form={customPricingForm}
            preserve={false}
            onFinish={addNewCustomPrice}
          >
            <div className="flex flex-col gap-2">
              <Form.Item
                name="customModifierName"
                label={t('ordersPage.priceBreakDown.nameLabel')}
                validateFirst
                validateDebounce={2000}
                rules={[
                  {
                    required: true,
                    message: t('ordersPage.priceBreakDown.requiredName'),
                  },
                  {
                    whitespace: true,
                    message: t('ordersPage.priceBreakDown.requiredName'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                  {
                    validator: nameValidator,
                  },
                ]}
              >
                <Input
                  placeholder={t('ordersPage.priceBreakDown.namePlaceholder')}
                  disabled={isModifyPricingModelOpen.isEdit}
                  maxLength={150}
                />
              </Form.Item>
              <Form.Item
                label={t('ordersPage.priceBreakDown.amountLabel')}
                rules={[
                  {
                    required: true,
                    message: t('ordersPage.priceBreakDown.requiredAmount'),
                  },
                  {
                    validator: amountValidator,
                  },
                ]}
                name="customModifierAmount"
              >
                <InputNumber
                  inputMode="decimal"
                  disabled={isModifyPricingModelOpen.isEdit}
                  className="bulk-adjust-input w-full"
                  placeholder="$00.00"
                  formatter={(value) => (value ? `$${value}` : '')}
                  maxLength={10}
                  onKeyDown={(event) =>
                    numberFieldValidator(event, { allowDecimals: true, allowNegative: true })
                  }
                  step={0.01}
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      </CustomModal>
      <header className="flex justify-between items-center pt-4">
        <h1 className="font-semibold text-xl">{orderDetails?.priceSet || 'N/A'}</h1>
        <Button
          className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
          icon={<EditPopupWhiteIcon />}
          onClick={() => {
            setIsModifyPricingModelOpen({ open: true, isEdit: false, customPriceId: undefined });
          }}
        >
          {t('ordersPage.priceBreakDown.modifierPricingButton')}
        </Button>
      </header>
      <main>
        <CustomAgGrid
          rowData={priceBreakdown?.data}
          loading={isFetching || isLoading}
          columnDefs={colDefs}
          className="!h-[73vh] lg:!h-[75vh]"
          gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
          onCellClicked={(params) => {
            if (params.colDef.field !== 'action') {
              viewPriceHandler(params);
            }
          }}
          emptyState={{
            title: t('ordersPage.priceBreakDown.emptyState.title'),
            description: t('ordersPage.priceBreakDown.emptyState.description'),
          }}
        />
      </main>
    </div>
  );
};
export default OrdersPricesBreakdownComponent;

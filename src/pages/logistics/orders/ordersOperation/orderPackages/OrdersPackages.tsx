import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomModal from '@/components/common/modal/CustomModal';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import { ValueFormatterParams, ICellRendererParams, CellClickedEvent } from 'ag-grid-community';
import { Button, Form, Image } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { IPriceBreakdown } from '../OrderPriceBreakdown/orderPriceBreakdown.types';
import { EyeIcon, PlusButtonIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { IPackage, IPackageModelState } from './orderPackages.types';
import { useConfig } from '@/contexts/ConfigContext';
import { ImageFormateIcon } from '@/assets/icons/imageFormateIcon';
import OrderPackageOperationForm from './orderPackagesOperation/OrderPackageOperationForm';
import { orderServiceHook } from '@/api/orders/useOrders';
import { IOrderPackages, IResponseOrderDto } from '@/api/orders/order.types';
import { useNotificationManager } from '@/hooks/useNotificationManger';

export interface IImagePreviewState {
  isPreviewVisible: boolean;
  src: undefined | string;
}

export interface IOrderPackagesProps {
  orderId: string | undefined;
}

const OrdersPackagesComponent = (props: IOrderPackagesProps) => {
  const { orderId } = props;
  const [isModelOpen, setIsModelOpen] = useState<IPackageModelState>({
    isOpen: false,
    isEdit: false,
    packageId: undefined,
  });
  const { t } = useLanguage();
  const notificationManager = useNotificationManager();

  const {
    data: orderPackages,
    refetch: refetchOrderPackages,
    isFetching,
    isLoading,
  } = orderServiceHook.useEntity<IOrderPackages[]>(`${orderId}/items`, {
    enabled: Boolean(orderId),
  });

  const { refetch: refetchOrderDetails } = orderServiceHook.useEntity<IResponseOrderDto>(
    orderId as string,
    {
      enabled: Boolean(orderId),
      retry: 0,
    }
  );

  const createOrderItemMutation = orderServiceHook.useCreateById(
    `items`,
    orderId as string,
    {
      onSuccess: () => {
        notificationManager.success({
          message: t('common.success'),
          description: t('ordersPage.packagesTab.notifications.packageAddedSuccess'),
        });
      },
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  const updateOrderItemMutation = orderServiceHook.useUpdate(
    {
      onSuccess: () => {
        notificationManager.success({
          message: t('common.success'),
          description: t('ordersPage.packagesTab.notifications.packageUpdatedSuccess'),
        });
      },
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  const [packagesForm] = Form.useForm();
  const [visible, setVisible] = useState<IImagePreviewState>({
    isPreviewVisible: false,
    src: undefined,
  });

  const { config } = useConfig();

  const viewPackageHandler = useCallback(
    (params: ICellRendererParams | CellClickedEvent) => {
      console.log({ ...params.data, imageUrl: params.data?.imageName });
      packagesForm.setFieldsValue({
        ...params.data,
        imageUrl: { file: { name: params.data?.imageName } },
      });
      setIsModelOpen({
        isOpen: true,
        isEdit: true,
        packageId: params.data?.id,
      });
    },
    [packagesForm]
  );

  const onFinish = async (values: any) => {
    const formatNumber = (num: string) => Number(Number(num).toFixed(2));

    const formattedValues = {
      ...values,
      height: formatNumber(values.height),
      totalWeight: formatNumber(values.totalWeight),
      length: formatNumber(values.length),
      width: formatNumber(values.width),
    };

    if (values?.imageUrl?.file && values?.imageUrl?.file instanceof File) {
      formattedValues.imageUrl = values.imageUrl?.file;
    } else {
      delete formattedValues.imageUrl;
    }

    try {
      if (isModelOpen.packageId) {
        await updateOrderItemMutation.mutateAsync({
          id: `${orderId}/items/${isModelOpen.packageId}`,
          data: {
            ...formattedValues,
            id: isModelOpen.packageId,
          },
        });
      } else {
        await createOrderItemMutation.mutateAsync(formattedValues);
      }

      await refetchOrderPackages();
      await refetchOrderDetails();
      setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined });
    } catch (error) {
      // Optional: log or handle error properly
    }
  };

  const colDefs: IColDef[] = useMemo(
    () => [
      {
        field: `packageTemplateName`,
        headerName: t('ordersPage.packagesTab.colDefs.packageType'),
        type: 'text',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: `quantity`,
        headerName: t('ordersPage.packagesTab.colDefs.quantity'),
        type: 'number',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        flex: 1,
      },
      {
        field: `totalWeight`,
        headerName: `${t('ordersPage.packagesTab.colDefs.CombinedWeight')} (${config.units?.weight})`,
        type: 'number',
        visible: true,
        sortable: true,
        unSortIcon: true,
        minWidth: 200,
        flex: 1,
      },
      {
        headerName: `${t('ordersPage.packagesTab.colDefs.CubicDimension')} (${config.units?.dimension})`,
        type: 'text',
        visible: true,
        sortable: false,
        unSortIcon: false,
        minWidth: 200,
        flex: 1,
        valueFormatter: (params: ValueFormatterParams<IPackage>) => {
          return `${params.data?.length} x ${params.data?.width} x ${params.data?.height}`;
        },
      },
      {
        //TODO:
        field: `imageName`,
        headerName: t('ordersPage.packagesTab.colDefs.image'),
        type: 'text',
        visible: true,
        sortable: false,
        unSortIcon: false,
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: ICellRendererParams) => {
          return params.value ? (
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => setVisible({ isPreviewVisible: true, src: params.data.imageUrl })}
            >
              <Icon component={ImageFormateIcon} />
              {params.value}
            </div>
          ) : (
            t('ordersPage.packagesTab.notAvailable')
          );
        },
      },
      {
        headerName: t('vehiclePage.colDefs.action'),
        pinned: 'right',
        field: 'action',
        maxWidth: 70,
        minWidth: 70,
        sortable: false,
        resizable: false,
        visible: true,
        cellRenderer: (params: ICellRendererParams<IPriceBreakdown>) => {
          return (
            <div className="flex gap-2 h-full justify-center items-center">
              <Icon
                component={EyeIcon}
                className="cursor-pointer"
                alt={t('ordersPage.packagesTab.actions.view')}
                value={t('ordersPage.packagesTab.actions.view')}
                onClick={() => viewPackageHandler(params)}
              />
            </div>
          );
        },
      },
    ],
    [config.units?.dimension, config.units?.weight, t, viewPackageHandler]
  );

  return (
    <div className="h-full">
      <CustomModal
        modalTitle={
          isModelOpen.isEdit
            ? t('ordersPage.packagesTab.viewPackage')
            : t('ordersPage.packagesTab.addPackage')
        }
        modalDescription={
          isModelOpen.isEdit
            ? t('ordersPage.packagesTab.viewPackageDesc')
            : t('ordersPage.packagesTab.addPackageDesc')
        }
        open={isModelOpen.isOpen}
        destroyOnClose
        maskClosable={false}
        onCancel={() => setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined })}
        className="!h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModelOpen({
                  isOpen: false,
                  isEdit: false,
                  packageId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="addCustomPrice"
                loading={createOrderItemMutation.isPending || updateOrderItemMutation.isPending}
                onClick={() => {
                  packagesForm.getFieldError('image');
                }}
              >
                {isModelOpen.isEdit ? t('common.update') : t('common.add')}
              </Button>
            }
          </div>
        }
      >
        <OrderPackageOperationForm form={packagesForm} onFinish={onFinish} />
      </CustomModal>
      <div className="pr-4">
        <Image
          hidden
          width={200}
          src={visible.src}
          preview={{
            visible: visible.isPreviewVisible,
            src:
              visible.src ||
              'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
            onVisibleChange: (value) => {
              setVisible({ ...visible, isPreviewVisible: value });
            },
          }}
        />
        <header className="flex justify-end items-center py-4">
          <Button
            className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            icon={<PlusButtonIcon />}
            onClick={() => {
              setIsModelOpen({ isOpen: true, isEdit: false, packageId: undefined });
            }}
          >
            {t('ordersPage.packagesTab.addPackage')}
          </Button>
        </header>
        <main>
          <CustomAgGrid
            rowData={orderPackages}
            loading={isFetching || isLoading}
            columnDefs={colDefs}
            onCellClicked={(params) => {
              if (params.colDef.field !== 'action' && params.colDef.field !== 'imageName') {
                viewPackageHandler(params);
              }
            }}
            className="!h-[74vh] lg:!h-[75vh]"
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
          />
        </main>
      </div>
    </div>
  );
};
export default OrdersPackagesComponent;

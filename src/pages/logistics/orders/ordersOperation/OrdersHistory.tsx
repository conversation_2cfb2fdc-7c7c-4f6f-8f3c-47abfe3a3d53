import React from 'react';
import { HistoryGrid } from '@/components/common/historyGrid';
import { useParams } from 'react-router-dom';

const OrdersHistoryComponent: React.FC = () => {
  const { id: orderId } = useParams();
  return (
    <div className="pr-4 pt-4">
      <HistoryGrid entityKey={'Order'} entityId={orderId} className="h-full" />
    </div>
  );
};

export default OrdersHistoryComponent;

import { TabsComponent } from '@/components/common/customTabs/CustomTabs';
import PageBreadCrumbsComponent from '@/components/specific/pageBreadCrumb/PageBreadCrumbComponent';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { useMemo } from 'react';
import { ROUTES } from '@/constant/RoutesConstant';
import { useParams } from 'react-router-dom';
import { TabsProps, Tag } from 'antd';
import { OrderStatusEnums } from '@/types/enums/orderStatus';
import { useLanguage } from '@/hooks/useLanguage';
import { orderServiceHook } from '@/api/orders/useOrders';
import NotFound404 from '@/components/common/statusFallbackPage/NotFound404';
import { translator } from '@/i18n/languageLoader';
import OrdersAttachmentsComponent from './orderAttachments/OrdersAttachments';
import OrdersPackagesComponent from './orderPackages/OrdersPackages';
import OrdersPricesBreakdownComponent from './OrderPriceBreakdown/OrdersPricesBreakdown';
import OrdersGeneralComponent from './OrdersGeneral';
import OrdersHistoryComponent from './OrdersHistory';
import { IResponseOrderDto } from '@/api/orders/order.types';

const OrdersInfoComponent = () => {
  const { id, tab } = useParams<{ id: string; tab: string }>();
  const { t } = useLanguage();

  const {
    data: orderDetails,
    // isFetching,
    isPending,
    error,
  } = orderServiceHook.useEntity<IResponseOrderDto>(id as string, {
    enabled: Boolean(id),
    retry: 0,
  });

  const breadCrumbObj: { [key: string]: string } = useMemo(() => {
    return {
      general: t('vehiclePage.breedCrumbs.general'),
      pricesBreakdown: t('ordersPage.pricesBreakdown'),
      packages: t('ordersPage.packages'),
      attachments: t('ordersPage.attachments'),
      history: t('priceSetPage.tabs.history'),
    };
  }, [t]);

  const breedCrumbPath = useMemo(() => {
    const tabKey = tab || 'general';

    return [
      {
        name: id && !isPending && !error ? orderDetails?.trackingNumber : 'Order',
        path: ROUTES.LOGISTIC.LOGISTICS_ORDERS,
      },
      {
        name: tab ? breadCrumbObj[tab || 'general'] : t('vehiclePage.breedCrumbs.general'),
        path: id
          ? ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', id).replace(':tab', tabKey)
          : ROUTES.LOGISTIC.LOGISTICS_ORDERS,
      },
    ];
  }, [breadCrumbObj, error, id, isPending, orderDetails?.trackingNumber, t, tab]);

  const status = 'InTransit';

  const orderStatusToReturn = {
    InTransit: {
      status: OrderStatusEnums.IN_TRANSIT,
      className: 'border-[#FDB022] bg-[#FFFAEB] text-[#F79009]',
    },
    Delivered: {
      status: OrderStatusEnums.COMPLETED,
      className: 'border-[#12B76A] text-[#12B76A] bg-[#ECFDF3]',
    },
    Cancelled: {
      status: OrderStatusEnums.CANCELLED,
      className: 'text-[#F04438] border-[#F04438] bg-[#FEF3F2]',
    },
    Draft: {
      status: OrderStatusEnums.DRAFT,
      className: 'text-[#20363F] border-[#CDD7DB] bg-[#FCFCFD]',
    },

    Submitted: {
      status: OrderStatusEnums.SUBMITTED,
      className: 'border-[#242A6A] bg-[#E3E5F6] text-[#242A6A]',
    },
  };

  const DefaultTabsForOrders: TabsProps['items'] = useMemo(
    () => [
      {
        key: 'general',
        label: translator('sidebar.general'),
        children: <OrdersGeneralComponent />,
        tabKey: 'General',
      },
      {
        key: 'pricesBreakdown',
        label: translator('ordersPage.pricesBreakdown'),
        children: <OrdersPricesBreakdownComponent />,
        tabKey: 'Prices Breakdown',
      },
      {
        key: 'packages',
        label: translator('ordersPage.packages'),
        children: <OrdersPackagesComponent orderId={orderDetails?.id} />,
        tabKey: 'Packages',
      },
      {
        key: 'attachments',
        label: translator('ordersPage.attachments'),
        children: <OrdersAttachmentsComponent />,
        tabKey: 'Attachments',
      },
      {
        key: 'history',
        label: translator('priceSetPage.tabs.history'),
        children: <OrdersHistoryComponent />,
        tabKey: 'History',
      },
    ],
    [orderDetails?.id]
  );

  const isOrderNotFound = (error?.response as any)?.data.code === '412001';

  return (
    <>
      <div className="flex flex-col gap-1 avoid-tab-position">
        <div>
          <PageHeadingComponent
            title={t('ordersPage.viewOrder')}
            isChildComponent
            children={
              !isOrderNotFound && (
                <div className="flex items-center gap-2 pt-[30px]">
                  <div className="flex items-center gap-2">
                    <span className="h-[10px] w-[10px] rounded-full bg-error-500  flex" />
                    <span className="font-[16px] text-[#D92D20]"> Due in 30 minutes</span>
                  </div>
                  <Tag
                    className={`${orderStatusToReturn[status].className ?? ' border-[#FDB022] bg-[#FFFAEB] text-[#FDB022]'} rounded-md h-[32px] w-[122px] flex align-middle items-center justify-center font-[14px]`}
                  >
                    {orderStatusToReturn[status].status}
                  </Tag>
                </div>
              )
            }
          />
          <PageBreadCrumbsComponent path={breedCrumbPath} />
        </div>

        {isOrderNotFound ? (
          <div className="h-[80vh] flex items-center justify-center">
            <NotFound404
              title="Order Not Found"
              description="The order you are looking for does not exist."
            />
          </div>
        ) : (
          <TabsComponent
            editableRoute={ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION}
            tabs={DefaultTabsForOrders}
            destroyInactiveTabPane
          />
        )}
      </div>
    </>
  );
};

export default OrdersInfoComponent;

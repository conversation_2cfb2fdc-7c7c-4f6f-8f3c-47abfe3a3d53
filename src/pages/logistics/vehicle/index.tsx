import { CellContextMenuEvent } from 'ag-grid-community';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DeleteIcon, deleteSvg, EyeIcon, PlusButtonIcon, TruckOutlinedIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import Icon from '@ant-design/icons/lib/components/Icon';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Divider, Button } from 'antd';
import './vehiclePage.css';
import { useLanguage } from '@/hooks/useLanguage';
import { GridNames } from '@/types/AppEvents';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { ROUTES } from '@/constant/RoutesConstant';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { IVehicle } from './vehicleTypes';
import ColumnManage from '@/components/specific/columnManage';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { IExtendedSortChangedEvent, ICellRendererParams, IColDef } from '@/types/AgGridTypes';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { getPaginationData } from '@/lib/helper';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { useConfig } from '@/contexts/ConfigContext';
import { vehicleServiceHooks } from '@/api/vehicle/useVehicle';
import { GetVehicleDto } from '@/api/vehicle/vehicle.types';
import { defaultPagination } from '@/constant/generalConstant';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { IAssignedFilters } from '../orders/orders.types';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';

const VehiclePage = () => {
  const { t } = useLanguage();
  const [searchText, setSearchText] = useState('');
  const [vehicles, setVehicles] = useState<GetVehicleDto[]>();
  const [cellData, setCellData] = useState<IVehicle>({} as IVehicle);
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const notificationManager = useNotificationManager();
  const gridRef = useRef<AgGridReact<IVehicle>>(null);
  const { navigate } = useNavigationContext();
  const { config } = useConfig();

  const {
    data: allVehicles,
    refetch: refetchVehicles,
    isFetching,
    isLoading,
  } = vehicleServiceHooks.useList(filterParams);

  const deleteMutation = vehicleServiceHooks.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.successDelete'),
      });
      await refetchVehicles();
    },
    onError: () => {
      notificationManager.success({
        message: t('common.error'),
        description: t('vehiclePage.notificationMessages.failedDelete'),
      });
    },
  });

  const deleteVehicleHandler = useCallback(
    async (vehicleId: string) => {
      await deleteMutation.mutateAsync(vehicleId);
    },
    [deleteMutation]
  );

  const deleteVehicleConfirmation = useCallback(
    (vehicleId: string) => {
      customAlert.error({
        title: t('vehiclePage.alert.deleteConfirmation'),
        message: t('vehiclePage.alert.deleteConfirmationMessage'),
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('vehiclePage.alert.firstBtnText'),
        secondButtonTitle: t('vehiclePage.alert.secondBtnText'),
        firstButtonFunction: async () => {
          await deleteVehicleHandler(vehicleId);
          customAlert.destroy();
        },
      });
    },
    [deleteVehicleHandler, t]
  );

  useEffect(() => {
    // TODO: find a way to remove this state
    if (allVehicles) {
      setVehicles(allVehicles?.data || []);
    }
  }, [allVehicles]);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.vehicleGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.vehicle.sortable.includes(field);
  }, []);

  const vehicleColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'fleetId',
        headerName: t('vehiclePage.colDefs.fleetId'),
        sortable: isColumnSortable('fleetId'),
        unSortIcon: isColumnSortable('fleetId'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'vehicleType',
        headerName: t('vehiclePage.colDefs.type'),
        visible: true,
        minWidth: 170,
        sortable: isColumnSortable('vehicleType'),
        unSortIcon: isColumnSortable('vehicleType'),
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'make',
        headerName: t('vehiclePage.colDefs.make'),
        unSortIcon: isColumnSortable('make'),
        sortable: isColumnSortable('make'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'model',
        headerName: t('vehiclePage.colDefs.model'),
        visible: true,
        sortable: isColumnSortable('model'),
        unSortIcon: isColumnSortable('model'),
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'licensePlate',
        headerName: t('vehiclePage.colDefs.licensePlate'),
        sortable: isColumnSortable('licensePlate'),
        unSortIcon: isColumnSortable('licensePlate'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: `maxWeight`,
        headerName: `${t('vehiclePage.colDefs.capacity')} (${config.units?.weight})`,
        type: 'number',
        visible: true,
        sortable: isColumnSortable('maxWeight'),
        unSortIcon: isColumnSortable('maxWeight'),
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: `currentOdometer`,
        headerName: `${t('vehiclePage.colDefs.odometer')} (${config.units?.distance})`,
        visible: true,
        type: 'number',
        unSortIcon: isColumnSortable('currentOdometer'),
        sortable: isColumnSortable('currentOdometer'),
        minWidth: 220,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: `isDeprecated`,
        headerName: t('vehiclePage.colDefs.isDeprecated'),
        visible: true,
        type: 'boolean',
        unSortIcon: false,
        sortable: false,
        minWidth: 220,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          const value = params.value ? t('common.yes') : t('common.no');
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'action',
        headerName: t('vehiclePage.colDefs.action'),
        pinned: 'right',
        maxWidth: 80,
        minWidth: 80,
        resizable: false,
        sortable: false,
        visible: true,
        cellRenderer: (params: ICellRendererParams<GetVehicleDto>) => {
          return (
            <div className="flex gap-2 h-full items-center">
              <Icon
                component={EyeIcon}
                className="cursor-pointer"
                alt="view"
                value={'view'}
                onClick={() =>
                  navigate(
                    `${ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(
                      ':id',
                      params.data.id as string
                    ).replace(':tab', 'general')}`
                  )
                }
              />

              <Icon
                component={DeleteIcon}
                className="cursor-pointer"
                alt="delete"
                onClick={() => deleteVehicleConfirmation(params.data.id as string)}
              />
            </div>
          );
        },
      },
    ];
  }, [
    config.units?.distance,
    config.units?.weight,
    deleteVehicleConfirmation,
    isColumnSortable,
    navigate,
    searchText,
    t,
  ]);

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const VehicleContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('vehiclePage.contextMenuItems.newVehicle'),
        key: 'newVehicle',
        icon: TruckOutlinedIcon,
        onClick: () => navigate(ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD.replace(':tab', 'general')),
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: () => <img src={deleteSvg} />,
        key: 'deleteVehicle',
        onClick: () => deleteVehicleConfirmation(cellData.id as string),
      },
    ];
  }, [cellData.id, deleteVehicleConfirmation, navigate, t]);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearQuickFilterFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearQuickFilterFunctionRef.current.handleClearAll();
  };

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);
  const paginationData = useMemo(() => getPaginationData(allVehicles), [allVehicles]);

  return (
    <>
      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title={t('vehiclePage.header.title')} />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  colDefs={vehicleColDefs}
                  onFilterApply={applyFilters}
                  searchInputPlaceholder={t('vehiclePage.header.searchVehicle')}
                  onSearch={triggerSearch}
                  setQuickFilters={() => {}}
                  setSelectedQuickFilterData={setSelectedQuickFilterData}
                  supportedFields={filterableModules.vehicle.advanceFilter}
                  clearAllFunctionRef={clearQuickFilterFunctionRef}
                  setFilterParams={setFilterParams}
                />
                <ColumnManage colDefs={vehicleColDefs} gridName={GridNames.vehicleGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="h-[40px] !m-0" />
              </div>
              <div className="flex gap-2 pt-5">
                <Button
                  className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                  onClick={() => {
                    navigate(ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD.replace(':tab', 'general'));
                  }}
                >
                  {t('vehiclePage.header.addNewVehicleBtnText')}
                </Button>
              </div>
            </div>
          </div>

          <main className="h-screen overflow-x-hidden overflow-y-auto bg-white">
            <ActiveFilters
              selectedQuickFilterData={selectedQuickFilterData}
              clearAllToDefault={clearAllToDefault}
              colDefs={vehicleColDefs}
            />
            <div className="mx-auto pr-6 py-5 h-full flex justify-center">
              <CustomAgGrid
                className={selectedQuickFilterData.length > 0 ? 'md:!h-[79vh]' : ''}
                gridRef={gridRef}
                rowData={vehicles}
                columnDefs={vehicleColDefs}
                loading={isLoading || isFetching}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                isContextMenu
                contextMenuItem={VehicleContextMenuItems}
                onContextMenu={(params: CellContextMenuEvent<IVehicle>) =>
                  setCellData(params.data as IVehicle)
                }
                gridName={GridNames.vehicleGrid}
                onCellClicked={(params) => {
                  if (params.colDef.field !== 'action') {
                    navigate(
                      `${ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(
                        ':id',
                        params.data.id as string
                      ).replace(':tab', 'general')}`
                    );
                  }
                }}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                emptyState={{
                  title:
                    searchText || selectedQuickFilterData.length > 0
                      ? t('common.noMatchesFound')
                      : t('vehiclePage.emptyState.title'),
                  description:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('vehiclePage.emptyState.description'),
                  link:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('vehiclePage.emptyState.link'),
                  onLinkAction: () =>
                    navigate(ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD.replace(':tab', 'general')),
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default VehiclePage;

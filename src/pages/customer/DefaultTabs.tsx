import { Tooltip, TabsProps } from 'antd';
import CustomerGeneralFormComponent from './customerOperations/customerGeneral/CustomerGeneralForm';
import CustomerContactsComponent from './customerOperations/customerContacts/CustomerContacts';
import CustomerAddressComponent from './customerOperations/customerAddress';
import CustomerSettingsComponent from './customerOperations/customerSettings/CustomerSettings';
import CustomerService from './customerOperations/customerServices';

export const DefaultTabsForCustomer = (idExists: boolean): TabsProps['items'] => [
  {
    key: 'general',
    label: 'General',
    children: <CustomerGeneralFormComponent />,
    tabKey: 'General',
  },
  {
    key: 'contacts',
    label: idExists ? (
      'Contacts'
    ) : (
      <Tooltip title="Please add a contact to enable access to this tab.">Contacts</Tooltip>
    ),
    children: <CustomerContactsComponent />,
    disabled: !idExists,
    tabKey: 'Contacts',
  },
  {
    key: 'address',
    label: idExists ? (
      'Address'
    ) : (
      <Tooltip title="Please add a contact to enable access to this tab.">Address</Tooltip>
    ),
    children: <CustomerAddressComponent />,
    disabled: !idExists,
    tabKey: 'Address',
  },
  {
    key: 'PriceSet',
    label: idExists ? (
      'Price set'
    ) : (
      <Tooltip title="Please add a contact to enable access to this tab.">Price set</Tooltip>
    ),
    children: <CustomerService />,
    disabled: !idExists,
    tabKey: 'Price set',
  },
  {
    key: 'settings',
    label: idExists ? (
      'Settings'
    ) : (
      <Tooltip title="Please add a contact to enable access to this tab.">Settings</Tooltip>
    ),
    children: <CustomerSettingsComponent />,
    disabled: !idExists,
    tabKey: 'Settings',
  },
];

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from 'ag-grid-react';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@components/specific/searchFilter/SearchFilterComponent';
import ColumnManage from '@components/specific/columnManage';
import { Button, Divider } from 'antd';
import { PlusButtonIcon } from '@assets/icons/plusButtonIcon';
import CustomAgGrid from '@components/common/agGrid/AgGrid';
import { AssignToOutlined } from '@assets/icons/assignToOutlined';
import { deleteSvg, EmailCredentialIcon } from '@/assets';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { GridNames } from '@/types/AppEvents.ts';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { customerColDefs } from './defaultColumns';
import { useLanguage } from '@/hooks/useLanguage';
import { CellContextMenuEvent, ICellRendererParams } from 'ag-grid-community';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { ICustomer } from '@/api/customer/customer.types';
import { customerHook } from '@/api/customer/useCustomer';
import { emit } from '@contexts/PulseContext.tsx';
import { getPaginationData } from '@/lib/helper';
import { defaultPagination } from '@/constant/generalConstant';
import { IAssignedFilters } from '../logistics/orders/orders.types';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { advanceFilterObjectMapper, maskQuickFilterData } from '@/lib/SearchFilterTypeManage';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { GridIdConstant } from '@/constant/GridIdConstant';

const Customer = () => {
  const gridRef = useRef<AgGridReact<ICustomer>>(null);
  const [customers, setCustomers] = useState<ICustomer[]>();

  const [searchText, setSearchText] = useState('');
  const notificationManager = useNotificationManager();
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [cellData, setCellData] = useState<CellContextMenuEvent<ICellRendererParams>>(
    {} as CellContextMenuEvent<ICellRendererParams>
  );
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);

  const {
    data: customerList,
    refetch: refetchCustomerList,
    isLoading,
    isFetching,
  } = customerHook.useList(filterParams);
  const paginationData = useMemo(() => getPaginationData(customerList), [customerList]);

  useEffect(() => {
    emit('columnManager:ready', {
      gridName: GridNames.customerGrid,
    });
    if (customerList && customerList?.data) {
      setCustomers(customerList?.data);
    }
  }, [customerList]);

  const deleteMutation = customerHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.customerDeletedSuccessfully'),
      });
      customAlert.destroy();
      await refetchCustomerList();
    },
  });

  const handleDeletePopup = useCallback(
    (params: ICellRendererParams) => {
      customAlert.error({
        title: t('dashboard.customer.confirmDeleteCustomer', {
          companyName: params?.data?.companyName,
        }),
        message: t('notifications.warningDescription'),
        firstButtonFunction: async () => {
          deleteMutation.mutateAsync(params?.data?.id);

          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [deleteMutation, t]
  );

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.customer.sortable.includes(field);
  }, []);

  const customerColumnDefs: IColDef[] = useMemo(
    () => customerColDefs(searchText, handleDeletePopup, t, isColumnSortable, navigate),
    [navigate, searchText, handleDeletePopup, t, isColumnSortable]
  );

  const customerContextMenuItems: any = useMemo(() => {
    return [
      {
        label: t('dashboard.customer.newCustomer'),
        key: 'newCustomer',
        icon: AssignToOutlined as React.ElementType,
        onClick: () => navigate(ROUTES.CUSTOMER.CUSTOMER_ADD),
      },
      {
        label: t('dashboard.customer.emailLoginDetails'),
        icon: EmailCredentialIcon as React.ElementType,
        key: 'emailLoginDetails',
        onClick: () => {
          // window.print();
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
        onClick: () => handleDeletePopup(cellData as unknown as ICellRendererParams),
      },
    ];
  }, [cellData, handleDeletePopup, navigate, t]);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={'Customers'} />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                colDefs={customerColumnDefs}
                onFilterApply={applyFilters}
                onSearch={triggerSearch}
                isSetQuickFilter={false}
                searchInputPlaceholder="Search customer"
                setSelectedQuickFilterData={setSelectedQuickFilterData}
                supportedFields={filterableModules.customer.advanceFilter}
                clearAllFunctionRef={clearAllFunctionRef}
                setFilterParams={setFilterParams}
              />

              <ColumnManage colDefs={customerColumnDefs} gridName={GridNames.customerGrid} />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
            </div>
            <div className="pt-0 md:pt-5">
              <Link to={ROUTES.CUSTOMER.CUSTOMER_ADD}>
                <Button
                  className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                >
                  {t('dashboard.customer.addCustomer')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <ActiveFilters
          selectedQuickFilterData={selectedQuickFilterData}
          clearAllToDefault={clearAllToDefault}
          colDefs={customerColumnDefs}
        />
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto pr-6 py-5 flex justify-center items-center">
            <CustomAgGrid
              className={selectedQuickFilterData.length > 0 ? 'md:!h-[79vh]' : ''}
              gridRef={gridRef}
              rowData={customers}
              gridId={GridIdConstant.GRID_WRAPPER_FOR_SMALL}
              loading={isLoading || isFetching}
              columnDefs={customerColumnDefs}
              isContextMenu
              contextMenuItem={customerContextMenuItems}
              onCellClicked={(params) => {
                if (params.colDef.field !== 'action') {
                  navigate(
                    ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':id', params.data.id).replace(
                      ':tab',
                      'general'
                    )
                  );
                }
              }}
              onContextMenu={(params: CellContextMenuEvent<ICellRendererParams>) =>
                setCellData(params)
              }
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
              gridName={GridNames.customerGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('dashboard.customer.emptyState.title'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('dashboard.customer.emptyState.description'),
                link:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('dashboard.customer.emptyState.link'),
                onLinkAction: () => {
                  navigate(ROUTES.CUSTOMER.CUSTOMER_ADD);
                },
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Customer;

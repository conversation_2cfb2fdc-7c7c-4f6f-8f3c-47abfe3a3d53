.customer-general-divider {
  @apply !m-0 !text-primary-200 text-[400] font-[14px];
  border-block-start: var(--primary-200) !important;
  @apply pb-3;
}

.customer-general-form {
  @apply flex flex-wrap w-[100%] gap-x-[25px];
  @apply p-3 pr-6;
  @apply overflow-y-auto max-h-[80vh];
}

.customer-general-form-item {
  @apply w-[80%] md:w-[45%] lg:w-[30%] 2xl:w-[31%] 3xl:w-[32%];
}

.customer-general-form-maskedItem {
  @apply w-[80%] md:w-[45%] lg:w-[30%] 2xl:w-[31%] 3xl:w-[32%];
}

.customer-general-input {
  @apply h-[40px];
}
.customer-general-form-item:first-child {
  width: 100%;
}

.customer-general-form-item > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
}

.customer-general-form-item > .ant-form-item-row > .ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-family);
}

.customer-general-form-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}

.customer-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
}

.customer-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
}

.customer-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}

.general-form-item-category {
  @apply w-[80%] md:w-[45%] lg:w-[32%];
}

.general-form-item-category > .ant-form-item-row > .ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
}

.customer-general-input > .ant-input-wrapper > .ant-input-group-addon {
  background: #f8fbfc;
  height: 40px;
}

.customer-general-maskedInput > .ant-input-wrapper > .ant-input-group-addon {
  background: #f8fbfc;
  height: 40px;
  padding: 0px;
  @apply 3xsm:w-[40%] md:w-[35%] lg:w-[35%] 2xl:w-[20%];
}

.address-popup-maskedInput > .ant-input-wrapper > .ant-input-group-addon {
  background: #f8fbfc;
  height: 40px;
  padding: 0px;
  @apply 3xsm:w-[40%] md:w-[35%] lg:w-[35%] 2xl:w-[20%];
}

.customer-general-input {
  height: 40px;
}

.customer-general-maskedInput > .ant-input-wrapper > .ant-input-outlined {
  height: 40px;
}

.general-form-item-category {
  @apply !h-[40px];
}

.customer-general-input
  > .ant-select-selector
  > .ant-select-selection-overflow
  > .ant-select-selection-overflow-item
  > .ant-select-selection-item {
  background: var(--primary-25);
}

.customer-general-input
  > .ant-select-selector
  > .ant-select-selection-overflow
  > .ant-select-selection-overflow-item
  > .ant-select-selection-item
  > .ant-select-selection-item-content {
  color: #0f4e6b;
  font-weight: 450;
}

.customer-general-input
  > .ant-input-wrapper
  > .ant-input-group-addon
  > .ant-select
  > .ant-select-selector {
  background: white !important;
}

.customer-active-key-item {
  @apply w-[99%] rounded-[8px] bg-primary-25 p-2;
}

.customer-active-key-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content {
  @apply flex justify-end;
}

.customer-active-key-item > .ant-row > .ant-col {
  font-size: 14px;
  font-weight: 500;
}

.customer-active-key-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}

.customer-active-key-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .ant-switch-checked {
  background: var(--primary-600) !important;
}

.customer-general-form-save {
  @apply bg-primary-600 text-white h-[40px];
  @apply hover:!bg-primary-600 hover:!text-white hover:!border-grey-300;
}

.customer-general-input > .ant-input-wrapper > .ant-input-outlined {
  height: 40px;
}

.general-form-item-prefix {
  @apply mb-0;
}

.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .customer-general-maskedInput {
  border-color: #ff4d4f;
  border-radius: 7px;
  border-width: 1px;
  border-style: solid;
}

.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .customer-general-maskedInput
  > .ant-input-wrapper
  > input:hover {
  border-color: #e5e7eb;
}

.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .customer-general-maskedInput
  > .ant-input-wrapper
  > input:focus {
  border-color: #e5e7eb;
  box-shadow: none;
}

.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .customer-general-maskedInput:hover {
  border-color: #ffa39e;
}

.combined-masked-input .ant-input-group > .ant-input:last-child {
  border-radius: 0px !important;
  border-right: transparent;
}

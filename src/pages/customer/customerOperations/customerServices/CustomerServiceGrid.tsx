import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { useLanguage } from '@/hooks/useLanguage';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ICustomerServiceGridProps } from './customerServiceTypes';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider } from 'antd';
import { PlusButtonIcon } from '@/assets';
import { emptyStateIcon2 } from '../../../../assets';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { searchData } from '@/lib/helper';
import { IGetAssignedPriceSets } from '@/api/customer/assignedPriceSets/assignedPriceSet.types';

const CustomerServiceGrid: React.FC<ICustomerServiceGridProps> = (props) => {
  const [services, setServices] = useState<IGetAssignedPriceSets[]>();
  const [serviceFilter, setServiceFilter] = useState<IGetAssignedPriceSets[]>([]);
  const { t } = useLanguage();
  const { setIsEdit, allServices, colDefs, setSearchText, searchText, noServiceAvailableInSystem } =
    props;
  const isServices = useMemo(() => allServices && allServices?.length > 0, [allServices]);

  useEffect(() => {
    if (allServices) {
      setServices(allServices || []);
      setServiceFilter(allServices || []);
    }
  }, [allServices]);

  const searchHandler = useCallback(
    (value: string) => {
      const results = searchData(
        serviceFilter,
        {
          query: value,
        },
        colDefs
      );
      setSearchText((prev) => ({
        ...prev,
        searchTextForAssigned: value,
      }));
      setServices(results);
    },
    [colDefs, serviceFilter, setSearchText]
  );

  return (
    <div className="h-full">
      <div className="flex-1 gap-3 flex flex-col overflow-hidden bg-white">
        <div className="flex items-end 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
          <div className="flex gap-3">
            <SearchFilterComponent
              onSearch={searchHandler}
              colDefs={colDefs}
              onFilterApply={() => {}}
              advanceFilter={false}
              searchInputPlaceholder="Search price set name"
            />
          </div>
          <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
          <Button
            className="w-[168px] border-[1px] h-[40px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            icon={<PlusButtonIcon />}
            type="primary"
            onClick={() => setIsEdit(true)}
          >
            {t('dashboard.customer.services.assignServices')}
          </Button>
        </div>
        <main className="h-full overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto pr-6 py-5 ">
            <CustomAgGrid
              gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
              rowData={services}
              columnDefs={colDefs}
              className="3xsm:!h-[50vh] md:!h-[75vh] lg:!h-[73vh] 3xl:!h-[74vh] 33xl"
              emptyState={{
                title: searchText.searchTextForAssigned
                  ? t('common.noMatchesFound')
                  : noServiceAvailableInSystem
                    ? t('dashboard.customer.services.emptyState.title')
                    : !isServices
                      ? t('priceSetPage.form.noServicesAssigned')
                      : t('dashboard.customer.services.emptyState.title'),
                description: noServiceAvailableInSystem
                  ? ''
                  : searchText.searchTextForAssigned
                    ? ''
                    : !isServices
                      ? t('priceSetPage.form.toAssignServiceClick')
                      : '',
                link: noServiceAvailableInSystem
                  ? ''
                  : !isServices
                    ? t('priceSetPage.form.here')
                    : '',
                onLinkAction: () => setIsEdit(true),
                image:
                  !searchText.searchTextForAssigned || !isServices ? emptyStateIcon2 : undefined,
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};
export default CustomerServiceGrid;

import { CellContextMenuEvent } from 'ag-grid-community';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AssignToOutlined, DeleteIcon, deleteSvg, EyeIcon, PlusButtonIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import Icon from '@ant-design/icons/lib/components/Icon';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Divider, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { GridNames } from '@/types/AppEvents';
import { ROUTES } from '@/constant/RoutesConstant';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import ColumnManage from '@/components/specific/columnManage';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { ICellRendererParams, IColDef } from '@/types/AgGridTypes';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
// import { vehicleServiceHooks } from '@/api/vehicle/useVehicle';
import { GetVehicleDto } from '@/api/vehicle/vehicle.types';
import { defaultPagination } from '@/constant/generalConstant';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { IPayment } from './paymentTypes';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { DownloadIcon } from '@/assets/icons/downloadIcon';

function generatePaymentData(count = 30) {
  const types = ['Mobile banking', 'Virtual card'];
  const payments = [];

  for (let i = 0; i < count; i++) {
    const id = `${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    const referenceNumber = Math.floor(************ + Math.random() * ************).toString();
    const invoiceNumber = `INV-2023-${String(i + 1).padStart(3, '0')}`;
    const utcDate = new Date().toISOString();
    const amount = (Math.random() * 100).toFixed(2);

    payments.push({
      id,
      customer: 'Gateway Fighter',
      referenceNumber: referenceNumber,
      date: utcDate,
      amount: parseFloat(amount),
      invoiceNumber: invoiceNumber,
      type: types[Math.floor(Math.random() * types.length)],
      created: utcDate,
    });
  }
  return payments;
}

const PaymentPage = () => {
  const { t } = useLanguage();
  const [searchText, setSearchText] = useState('');
  const [payments, setPayments] = useState<IPayment[]>();
  const [cellData, setCellData] = useState<IPayment>({} as IPayment);
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  // const notificationManager = useNotificationManager();
  const gridRef = useRef<AgGridReact<IPayment>>(null);
  const { navigate } = useNavigationContext();
  // const { config } = useConfig();

  // const {
  //   data: allVehicles,
  //   // refetch: refetchVehicles,
  //   isFetching,
  //   isLoading,
  // } = vehicleServiceHooks.useList(filterParams);

  // const deleteMutation = vehicleServiceHooks.useDelete({
  //   onSuccess: async () => {
  //     notificationManager.success({
  //       message: t('common.success'),
  //       description: t('vehiclePage.notificationMessages.successDelete'),
  //     });
  //     await refetchVehicles();
  //   },
  //   onError: () => {
  //     notificationManager.success({
  //       message: t('common.error'),
  //       description: t('vehiclePage.notificationMessages.failedDelete'),
  //     });
  //   },
  // });

  // const deleteVehicleHandler = useCallback(
  //   async (vehicleId: string) => {
  //     await deleteMutation.mutateAsync(vehicleId);
  //   },
  //   [deleteMutation]
  // );

  const deleteVehicleConfirmation = useCallback(
    (_vehicleId: string) => {
      customAlert.error({
        title: t('paymentPage.alert.confirmDelete'),
        message: t('paymentPage.alert.confirmDeleteMessage'),
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('vehiclePage.alert.firstBtnText'),
        secondButtonTitle: t('vehiclePage.alert.secondBtnText'),
        firstButtonFunction: async () => {
          // await deleteVehicleHandler(vehicleId);
          customAlert.destroy();
        },
      });
    },
    [t]
  );

  useEffect(() => {
    // TODO: find a way to remove this state
    setPayments(generatePaymentData());
    // if (allVehicles) {
    // }
  }, []);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.paymentGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  // const isColumnSortable = useCallback((field: string) => {
  //   return filterableModules.vehicle.sortable.includes(field);
  // }, []);

  const paymentColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'customer',
        headerName: t('paymentPage.colDefs.customer'),
        // sortable: isColumnSortable('fleetId'),
        // unSortIcon: isColumnSortable('fleetId'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'referenceNumber',
        headerName: t('paymentPage.colDefs.referenceNumber'),
        visible: true,
        minWidth: 170,
        // sortable: isColumnSortable('vehicleType'),
        // unSortIcon: isColumnSortable('vehicleType'),
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'date',
        headerName: t('paymentPage.colDefs.date'),
        // unSortIcon: isColumnSortable('make'),
        // sortable: isColumnSortable('make'),
        visible: true,
        type: 'date',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText
            ? highlightText(dateFormatter(params.value, 'DD/MM/YYYY'), searchText)
            : dateFormatter(params.value, 'DD/MM/YYYY');
        },
      },
      {
        field: 'amount',
        headerName: t('paymentPage.colDefs.amount'),
        visible: true,
        // sortable: isColumnSortable('model'),
        // unSortIcon: isColumnSortable('model'),
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(`$${params.value}`, searchText) : `$${params.value}`;
        },
      },
      {
        field: 'invoiceNumber',
        headerName: t('paymentPage.colDefs.invoiceNumber'),
        // sortable: isColumnSortable('licensePlate'),
        // unSortIcon: isColumnSortable('licensePlate'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: `type`,
        headerName: t('paymentPage.colDefs.type'),
        type: 'string',
        visible: true,
        // sortable: isColumnSortable('maxWeight'),
        // unSortIcon: isColumnSortable('maxWeight'),
        minWidth: 170,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'created',
        headerName: t('paymentPage.colDefs.created'),
        visible: true,
        type: 'date',
        // unSortIcon: isColumnSortable('currentOdometer'),
        // sortable: isColumnSortable('currentOdometer'),
        minWidth: 220,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText
            ? highlightText(dateFormatter(params.value), searchText)
            : dateFormatter(params.value);
        },
      },
      {
        field: 'action',
        headerName: t('paymentPage.colDefs.action'),
        pinned: 'right',
        maxWidth: 80,
        minWidth: 80,
        resizable: false,
        sortable: false,
        visible: true,
        cellRenderer: (params: ICellRendererParams<GetVehicleDto>) => {
          return (
            <div className="flex gap-2 h-full items-center">
              <Icon
                component={EyeIcon}
                className="cursor-pointer"
                alt="view"
                value={'view'}
                onClick={() =>
                  navigate(
                    `${ROUTES.BILLING.BILLING_EDIT_PAYMENT.replace(
                      ':id',
                      params.data.id as string
                    )}`
                  )
                }
              />

              <Icon
                component={DeleteIcon}
                className="cursor-pointer"
                alt="delete"
                onClick={() => deleteVehicleConfirmation(params.data.id as string)}
              />
            </div>
          );
        },
      },
    ];
  }, [deleteVehicleConfirmation, navigate, searchText, t]);

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const paymentContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('paymentPage.contextMenu.addPayment'),
        key: 'addNewPayment',
        icon: AssignToOutlined,
        onClick: () => navigate(ROUTES.BILLING.BILLING_ADD_PAYMENT),
      },
      {
        label: t('paymentPage.contextMenu.download'),
        key: 'downloadPayment',
        icon: DownloadIcon,
        onClick: ({ rowData }) => {
          rowData?.data?.id &&
            navigate(ROUTES.BILLING.BILLING_EDIT_PAYMENT.replace(':id', rowData?.data?.id));
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: () => <img src={deleteSvg} />,
        key: 'deletePayment',
        onClick: () => deleteVehicleConfirmation(cellData.id as string),
      },
    ];
  }, [cellData.id, deleteVehicleConfirmation, navigate, t]);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearQuickFilterFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearQuickFilterFunctionRef.current.handleClearAll();
  };
  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);
  // const paginationData = useMemo(() => getPaginationData(allVehicles), [allVehicles]);

  return (
    <>
      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title={t('paymentPage.header.title')} />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  colDefs={paymentColDefs}
                  onFilterApply={applyFilters}
                  searchInputPlaceholder={t('paymentPage.header.searchPlaceholder')}
                  onSearch={triggerSearch}
                  setQuickFilters={() => {}}
                  supportedFields={filterableModules.payment.advanceFilter}
                  clearAllFunctionRef={clearQuickFilterFunctionRef}
                  setFilterParams={setFilterParams}
                />
                <ColumnManage colDefs={paymentColDefs} gridName={GridNames.paymentGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="h-[40px] !m-0" />
              </div>
              <Button
                className=" h-[40px] border-[1px] mt-5 rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
                onClick={() => {
                  navigate(ROUTES.BILLING.BILLING_ADD_PAYMENT);
                }}
              >
                {t('paymentPage.header.addPaymentBtn')}
              </Button>
            </div>
          </div>

          <main className="h-screen overflow-x-hidden overflow-y-auto bg-white">
            <ActiveFilters
              selectedQuickFilterData={selectedQuickFilterData}
              clearAllToDefault={clearAllToDefault}
              colDefs={paymentColDefs}
            />
            <div className="mx-auto pr-6 py-5 h-full flex justify-center">
              <CustomAgGrid
                className={selectedQuickFilterData.length > 0 ? 'md:!h-[79vh]' : '!h-[89vh]'}
                gridRef={gridRef}
                rowData={payments}
                columnDefs={paymentColDefs}
                // loading={isLoading || isFetching}
                // onSortChanged={(params: IExtendedSortChangedEvent) =>
                //   setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                // }
                isContextMenu
                contextMenuItem={paymentContextMenuItems}
                onContextMenu={(params: CellContextMenuEvent<IPayment>) =>
                  setCellData(params.data as IPayment)
                }
                gridName={GridNames.paymentGrid}
                // paginationProps={{
                //   ...paginationData,
                //   onPaginationChange(page, pageLimit) {
                //     setFilterParams((prev) => ({
                //       ...prev,
                //       pageNumber: page,
                //       pageSize: pageLimit,
                //     }));
                //   },
                // }}
                emptyState={{
                  title:
                    searchText || selectedQuickFilterData.length > 0
                      ? t('common.noMatchesFound')
                      : t('paymentPage.emptyState.title'),
                  description:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('paymentPage.emptyState.description'),
                  link:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('paymentPage.emptyState.link'),
                  onLinkAction: () => navigate(ROUTES.BILLING.BILLING_ADD_PAYMENT),
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default PaymentPage;

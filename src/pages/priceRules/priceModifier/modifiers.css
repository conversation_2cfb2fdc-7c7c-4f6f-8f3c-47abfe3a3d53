.general-form-item > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
  font-weight: 500;
}
.general-form-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.applicable-range-item2 > .ant-form-item-row > .ant-form-item-label > label {
  color: white;
}
.applicable-range-item1 > .ant-form-item-row > .ant-form-item-label > label::before {
  display: none !important;
}
.applicable-range-item2 > .ant-form-item-row > .ant-form-item-label > label::before {
  display: none !important;
}
.applicable-range-input
  > .ant-input-number-wrapper
  > .ant-input-number
  > .ant-input-number-input-wrap
  > input {
  height: 40px;
}
.initial-value-input > .ant-input-number-input-wrap > input {
  height: 40px;
}
.input-number-start > .ant-input-number-wrapper > .ant-input-number {
  height: 40px;
  display: flex;
  align-items: center;
}
.select-item > .ant-select-selector {
  gap: 10px;
}
.select-item > .ant-select-selector > .ant-select-selection-wrap > .ant-select-selection-item {
  font-size: large;
}
.required-tag-item > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 1px;
}
.required-tag-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.modifier-select-behavior {
  height: 40px !important;
}
.modifier-form-item > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
  font-weight: 600;
}
.modifier-form-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}

{"name": "transport_app", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "20.17.0", "npm": "please-use-pnpm", "pnpm": ">= 9.11.0"}, "packageManager": "pnpm@9.11.0", "scripts": {"dev": "vite --mode development", "build": "tsc -b && vite build", "build:dev": "tsc -b && vite build --mode development", "build:staging": "tsc -b && vite build --mode staging", "build:prod": "tsc -b && vite build --mode production", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@devforgets/pulse": "^0.2.3", "@react-google-maps/api": "^2.20.3", "@tanstack/react-query": "5.x", "@tanstack/react-query-devtools": "5.x", "@types/google-maps": "^3.2.6", "@types/node": "^22.7.5", "ag-grid-community": "^32.3.2", "ag-grid-react": "^32.3.2", "antd": "^5.21.4", "antd-mask-input": "^2.0.7", "axios": "^1.7.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^11.11.11", "lucide-react": "^0.451.0", "motion": "^12.0.11", "posthog-js": "^1.236.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "tailwind-merge": "^3.0.1"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "prettier": "^3.2.5", "tailwindcss": "^3.4.13", "typescript": "^5.5.3", "typescript-eslint": "^7.0.0", "vite": "^5.4.8"}}